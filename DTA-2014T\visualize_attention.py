import numpy as np
import os
import glob
import cv2
import torch
from collections import OrderedDict
import matplotlib.pyplot as plt
from utils import video_augmentation
from slr_network import SLRModel
import utils

class AttentionVisualizer:
    def __init__(self, model_path, dataset='phoenix2014-T', gpu_id=0):
        self.dataset = dataset
        self.gpu_id = gpu_id
        self.device = utils.GpuDataParallel()
        self.device.set_device(gpu_id)
        
        # 加载词典
        self.dict_path = f'./preprocess/{dataset}/gloss_dict.npy'
        self.gloss_dict = np.load(self.dict_path, allow_pickle=True).item()
        
        # 设置注意力参数
        self.attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        
        # 初始化模型
        self.model = self._init_model(model_path)
        self.spatial_attention_maps = []
        self.temporal_attention_maps = []
        
        # 注册钩子
        self._register_hooks()
        
    def _init_model(self, model_path):
        model = SLRModel(
            num_classes=len(self.gloss_dict)+1,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=self.gloss_dict,
            loss_weights={'ConvCTC': 1.0, 'SeqCTC': 1.0, 'Dist': 25.0},
            attention_params=self.attention_params
        )
        
        # 加载预训练权重
        state_dict = torch.load(model_path)['model_state_dict']
        state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
        
        # 创建新的state_dict，只加载匹配的参数
        new_state_dict = model.state_dict()
        for name, param in state_dict.items():
            if name in new_state_dict:
                if new_state_dict[name].shape == param.shape:
                    new_state_dict[name] = param
                else:
                    print(f"Skipping parameter {name} due to shape mismatch: {new_state_dict[name].shape} vs {param.shape}")
        
        model.load_state_dict(new_state_dict, strict=False)
        model = model.to(self.device.output_device)
        model.train()
        return model
    
    def _register_hooks(self):
        def attention_hook(module, input, output):
            # 获取注意力权重
            self.spatial_attention_maps.append(output.detach().cpu())
            
        # 注册空间和时间注意力的钩子
        if 'phoenix' in self.dataset:
            self.model.conv2d.corr2.register_forward_hook(attention_hook)
        else:
            self.model.conv2d.corr3.register_forward_hook(attention_hook)
    
    def process_video(self, video_path):
        """处理单个视频或图像序列"""
        if os.path.isdir(video_path):
            img_list = sorted(glob.glob(os.path.join(video_path, '*')))
            img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]
        else:
            cap = cv2.VideoCapture(video_path)
            img_list = []
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                img_list.append(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            cap.release()
        
        # 视频预处理
        transform = video_augmentation.Compose([
            video_augmentation.CenterCrop(224),
            video_augmentation.Resize(1.0),
            video_augmentation.ToTensor(),
        ])
        vid, _ = transform(img_list, [], None)
        vid = vid.float() / 127.5 - 1
        vid = vid.unsqueeze(0)
        
        # 处理填充
        left_pad = self._calculate_padding(vid)
        vid = self._pad_video(vid, left_pad)
        
        return vid, img_list
    
    def _calculate_padding(self, vid):
        left_pad = 0
        last_stride = 1
        total_stride = 1
        kernel_sizes = ['K5', "P2", 'K5', "P2"]
        for ks in kernel_sizes:
            if ks[0] == 'K':
                left_pad = left_pad * last_stride 
                left_pad += int((int(ks[1])-1)/2)
            elif ks[0] == 'P':
                last_stride = int(ks[1])
                total_stride = total_stride * last_stride
        return left_pad
    
    def _pad_video(self, vid, left_pad):
        max_len = vid.size(1)
        video_length = torch.LongTensor([np.ceil(vid.size(1) / 4) * 4 + 2*left_pad])
        right_pad = int(np.ceil(max_len / 4)) * 4 - max_len + left_pad
        max_len = max_len + left_pad + right_pad
        vid = torch.cat(
            (
                vid[0,0][None].expand(left_pad, -1, -1, -1),
                vid[0],
                vid[0,-1][None].expand(max_len - vid.size(1) - left_pad, -1, -1, -1),
            )
            , dim=0).unsqueeze(0)
        return vid
    
    def visualize(self, video_path, output_dir='./attention_vis'):
        """生成注意力可视化"""
        # 清空之前的注意力图
        self.spatial_attention_maps = []
        self.temporal_attention_maps = []
        
        # 处理视频
        vid, original_frames = self.process_video(video_path)
        vid = self.device.data_to_device(vid)
        vid_lgt = self.device.data_to_device(torch.LongTensor([vid.size(1)]))
        
        # 前向传播
        with torch.no_grad():
            self.model(vid, vid_lgt)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取注意力图
        attention_maps = self.spatial_attention_maps[0]  # 使用最后一层的注意力图
        
        # 生成可视化结果
        self._visualize_attention_maps(
            original_frames,
            attention_maps,
            output_dir
        )
    
    def _visualize_attention_maps(self, original_frames, attention_maps, output_dir):
        """生成注意力图的可视化"""
        N, C, T, H, W = attention_maps.shape
        
        # 对每个时间步生成可视化
        for t in range(T):
            # 计算注意力图
            attention_map = attention_maps[0, :, t].mean(0)  # 平均所有通道
            attention_map = (attention_map - attention_map.min()) / (attention_map.max() - attention_map.min())
            attention_map = cv2.resize(attention_map.numpy(), (224, 224))
            
            # 原始帧
            frame = original_frames[t]
            frame = cv2.resize(frame, (224, 224))
            
            # 生成热力图
            heatmap = cv2.applyColorMap(np.uint8(255 * attention_map), cv2.COLORMAP_JET)
            
            # 叠加原始图像和热力图
            alpha = 0.6
            overlay = cv2.addWeighted(frame, 1-alpha, heatmap, alpha, 0)
            
            # 保存结果
            cv2.imwrite(os.path.join(output_dir, f'attention_frame_{t:03d}.jpg'), 
                       cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))
        
        # 生成视频
        self._frames_to_video(output_dir)
    
    def _frames_to_video(self, frames_dir, fps=10):
        """将帧序列转换为视频"""
        frame_files = sorted(glob.glob(os.path.join(frames_dir, 'attention_frame_*.jpg')))
        if not frame_files:
            return
        
        frame = cv2.imread(frame_files[0])
        height, width, _ = frame.shape
        
        video_path = os.path.join(frames_dir, 'attention_visualization.mp4')
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
        
        for frame_file in frame_files:
            frame = cv2.imread(frame_file)
            out.write(frame)
        
        out.release()

if __name__ == '__main__':
    # 使用示例
    model_path = './pretrain_model/_best_model.pt'  # 使用正确的预训练模型路径
    visualizer = AttentionVisualizer(model_path)
    
    # 可以处理视频文件或图像序列目录
    video_path = './dataset/PHOENIX-2014-T-release-v3/PHOENIX-2014-T/features/fullFrame-256x256px/01April_2010_Thursday_heute_default-1'
    visualizer.visualize(video_path, output_dir='./attention_vis') 