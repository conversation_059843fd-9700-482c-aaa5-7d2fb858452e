import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 设置输出目录
output_dir = "./param_tuning_results"
results_file = os.path.join(output_dir, "results.txt")

# 读取结果
results = []
with open(results_file, "r") as f:
    for line in f:
        # 跳过注释行和空行
        if line.startswith("#") or not line.strip():
            continue

        parts = line.strip().split(", ")
        config = {}
        for part in parts:
            # 检查是否有等号
            if "=" not in part:
                continue

            try:
                key, value = part.split("=", 1)  # 只分割第一个等号
                key = key.strip()
                value = value.strip()

                if key == "dev_wer":
                    # 处理百分比符号
                    value = value.replace("%", "")
                    config[key] = float(value)
                elif key == "time":
                    # 处理时间单位
                    value = value.replace("s", "")
                    config[key] = float(value)
                else:
                    config[key] = value
            except Exception as e:
                print(f"Warning: Could not parse part '{part}': {e}")
                continue

        # 只添加非空的配置
        if config:
            results.append(config)

# 检查是否有结果
if not results:
    print("没有找到有效的结果。")
    exit(0)

# 创建DataFrame
df = pd.DataFrame(results)

# 检查必要的列是否存在
required_columns = ["max_window_size", "kernel_sizes", "reduction_ratio", "dev_wer"]
missing_columns = [col for col in required_columns if col not in df.columns]
if missing_columns:
    print(f"缺少必要的列: {missing_columns}")
    print("可用的列: {df.columns.tolist()}")
    exit(0)

# 按性能排序
df = df.sort_values("dev_wer")

# 打印最佳配置
print("最佳配置:")
for i, (_, row) in enumerate(df.head(min(5, len(df))).iterrows()):
    print(f"{i+1}. max_window_size={row['max_window_size']}, kernel_sizes={row['kernel_sizes']}, reduction_ratio={row['reduction_ratio']}, dev_wer={row['dev_wer']:.2f}%")

# 创建可视化目录
vis_dir = os.path.join(output_dir, "visualizations")
os.makedirs(vis_dir, exist_ok=True)

# 检查数据是否足够进行可视化
if len(df) < 2:
    print("数据点不足，无法生成可视化。")
else:
    try:
        # 分析max_window_size的影响
        if len(df['max_window_size'].unique()) > 1:
            plt.figure(figsize=(10, 6))
            sns.boxplot(x="max_window_size", y="dev_wer", data=df)
            plt.title("Max Window Size vs. Dev WER")
            plt.xlabel("Max Window Size")
            plt.ylabel("Dev WER (%)")
            plt.savefig(os.path.join(vis_dir, "max_window_size.png"))
            print(f"\u751f成图表: {os.path.join(vis_dir, 'max_window_size.png')}")

        # 分析reduction_ratio的影响
        if len(df['reduction_ratio'].unique()) > 1:
            plt.figure(figsize=(10, 6))
            sns.boxplot(x="reduction_ratio", y="dev_wer", data=df)
            plt.title("Reduction Ratio vs. Dev WER")
            plt.xlabel("Reduction Ratio")
            plt.ylabel("Dev WER (%)")
            plt.savefig(os.path.join(vis_dir, "reduction_ratio.png"))
            print(f"\u751f成图表: {os.path.join(vis_dir, 'reduction_ratio.png')}")

        # 分析kernel_sizes的影响
        if len(df['kernel_sizes'].unique()) > 1:
            plt.figure(figsize=(12, 6))
            sns.boxplot(x="kernel_sizes", y="dev_wer", data=df)
            plt.title("Kernel Sizes vs. Dev WER")
            plt.xlabel("Kernel Sizes")
            plt.ylabel("Dev WER (%)")
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, "kernel_sizes.png"))
            print(f"\u751f成图表: {os.path.join(vis_dir, 'kernel_sizes.png')}")

        # 创建热力图 - 只有当有多个窗口大小和缩减比例时才创建
        if len(df['max_window_size'].unique()) > 1 and len(df['reduction_ratio'].unique()) > 1:
            pivot = df.pivot_table(
                values="dev_wer",
                index="max_window_size",
                columns="reduction_ratio",
                aggfunc="mean"
            )
            plt.figure(figsize=(10, 8))
            sns.heatmap(pivot, annot=True, cmap="YlGnBu", fmt=".2f")
            plt.title("Dev WER (%) by Max Window Size and Reduction Ratio")
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, "heatmap.png"))
            print(f"\u751f成图表: {os.path.join(vis_dir, 'heatmap.png')}")
    except Exception as e:
        print(f"\u751f成可视化时出错: {e}")

if len(df) > 0:
    # 输出最佳配置的详细信息
    best_config = df.iloc[0]
    print("\n最佳配置详细信息:")
    for col in df.columns:
        if col in best_config:
            if col == 'dev_wer':
                print(f"{col} = {best_config[col]:.2f}% (词错误率)")
            elif col == 'time':
                print(f"{col} = {best_config[col]:.2f}s (运行时间)")
            elif col == 'max_window_size':
                print(f"{col} = {best_config[col]} (最大窗口大小)")
            elif col == 'kernel_sizes':
                print(f"{col} = {best_config[col]} (卷积核大小)")
            elif col == 'reduction_ratio':
                print(f"{col} = {best_config[col]} (通道缩减比例)")
            else:
                print(f"{col} = {best_config[col]}")

    # 计算每个超参数的平均WER
    print("\n各超参数的平均WER:")
    print("\n1. 最大窗口大小 (max_window_size):")
    for size in sorted(df['max_window_size'].unique()):
        avg_wer = df[df['max_window_size'] == size]['dev_wer'].mean()
        print(f"   {size}: {avg_wer:.2f}%")

    print("\n2. 卷积核大小 (kernel_sizes):")
    for kernel in df['kernel_sizes'].unique():
        avg_wer = df[df['kernel_sizes'] == kernel]['dev_wer'].mean()
        print(f"   {kernel}: {avg_wer:.2f}%")

    print("\n3. 通道缩减比例 (reduction_ratio):")
    for ratio in sorted(df['reduction_ratio'].unique()):
        avg_wer = df[df['reduction_ratio'] == ratio]['dev_wer'].mean()
        print(f"   {ratio}: {avg_wer:.2f}%")

    # 创建配置文件
    best_config_file = os.path.join(output_dir, "best_config.txt")
    with open(best_config_file, "w") as f:
        f.write("# 动态时间注意力机制最佳超参数配置\n\n")

        # 写入最佳配置
        f.write("## 最佳配置\n")
        for col in df.columns:
            if col in best_config:
                if col == 'dev_wer':
                    f.write(f"{col} = {best_config[col]:.2f}% (词错误率)\n")
                elif col == 'time':
                    f.write(f"{col} = {best_config[col]:.2f}s (运行时间)\n")
                elif col == 'max_window_size':
                    f.write(f"{col} = {best_config[col]} (最大窗口大小)\n")
                elif col == 'kernel_sizes':
                    f.write(f"{col} = {best_config[col]} (卷积核大小)\n")
                elif col == 'reduction_ratio':
                    f.write(f"{col} = {best_config[col]} (通道缩减比例)\n")
                else:
                    f.write(f"{col} = {best_config[col]}\n")

        # 写入每个超参数的平均WER
        f.write("\n## 各超参数的平均WER\n")

        f.write("\n### 1. 最大窗口大小 (max_window_size)\n")
        for size in sorted(df['max_window_size'].unique()):
            avg_wer = df[df['max_window_size'] == size]['dev_wer'].mean()
            f.write(f"   {size}: {avg_wer:.2f}%\n")

        f.write("\n### 2. 卷积核大小 (kernel_sizes)\n")
        for kernel in df['kernel_sizes'].unique():
            avg_wer = df[df['kernel_sizes'] == kernel]['dev_wer'].mean()
            f.write(f"   {kernel}: {avg_wer:.2f}%\n")

        f.write("\n### 3. 通道缩减比例 (reduction_ratio)\n")
        for ratio in sorted(df['reduction_ratio'].unique()):
            avg_wer = df[df['reduction_ratio'] == ratio]['dev_wer'].mean()
            f.write(f"   {ratio}: {avg_wer:.2f}%\n")

        # 写入所有测试的配置
        f.write("\n## 所有测试的配置\n")
        for i, (_, row) in enumerate(df.iterrows()):
            f.write(f"{i+1}. max_window_size={row['max_window_size']}, kernel_sizes={row['kernel_sizes']}, reduction_ratio={row['reduction_ratio']}, dev_wer={row['dev_wer']:.2f}%")
            if 'time' in row:
                f.write(f", time={row['time']:.2f}s")
            f.write("\n")

    print(f"\n最佳配置已保存到 {best_config_file}")
