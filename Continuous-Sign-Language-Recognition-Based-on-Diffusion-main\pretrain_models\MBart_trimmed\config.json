{"_name_or_path": "./mbart-large-cc25", "_num_labels": 3, "activation_dropout": 0.0, "activation_function": "gelu", "add_bias_logits": false, "add_final_layer_norm": true, "architectures": ["MBartForConditionalGeneration"], "attention_dropout": 0.0, "bos_token_id": 0, "classif_dropout": 0.0, "classifier_dropout": 0.0, "d_model": 1024, "decoder_attention_heads": 16, "decoder_ffn_dim": 4096, "decoder_layerdrop": 0.0, "decoder_layers": 12, "dropout": 0.1, "encoder_attention_heads": 16, "encoder_ffn_dim": 4096, "encoder_layerdrop": 0.0, "encoder_layers": 12, "eos_token_id": 2, "forced_eos_token_id": 2, "id2label": {"0": "LABEL_0", "1": "LABEL_1", "2": "LABEL_2"}, "init_std": 0.02, "is_encoder_decoder": true, "label2id": {"LABEL_0": 0, "LABEL_1": 1, "LABEL_2": 2}, "max_length": 1024, "max_position_embeddings": 1024, "model_type": "mbart", "normalize_before": true, "normalize_embedding": true, "num_beams": 5, "num_hidden_layers": 12, "output_past": true, "pad_token_id": 1, "scale_embedding": true, "static_position_embeddings": false, "task_specific_params": {"translation_en_to_ro": {"decoder_start_token_id": 250020}}, "torch_dtype": "float32", "transformers_version": "4.41.2", "use_cache": true, "vocab_size": 2172}