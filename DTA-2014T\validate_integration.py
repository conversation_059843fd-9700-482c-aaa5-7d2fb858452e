#!/usr/bin/env python3
"""
验证脚本：测试集成后的模型是否能正常训练
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os
from torch.utils.data import DataLoader, TensorDataset

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_dummy_dataset(num_samples=10, seq_len=64, num_classes=1296):
    """创建虚拟数据集用于测试"""
    # 视频数据 (batch, time, channels, height, width)
    videos = torch.randn(num_samples, seq_len, 3, 224, 224)
    
    # 视频长度
    vid_lengths = torch.randint(seq_len//2, seq_len+1, (num_samples,))
    
    # 标签数据
    max_label_len = 20
    labels = torch.randint(1, num_classes, (num_samples, max_label_len))
    
    # 标签长度
    label_lengths = torch.randint(5, max_label_len+1, (num_samples,))
    
    return TensorDataset(videos, vid_lengths, labels, label_lengths)

def test_training_step():
    """测试训练步骤"""
    print("Testing training step...")
    
    try:
        from slr_network import SLRModel
        
        # 创建模型参数
        gloss_dict = {f'gloss_{i}': [i, f'gloss_{i}'] for i in range(1295)}
        
        model_args = {
            'num_classes': 1296,
            'c2d_type': 'resnet18',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 512,
            'gloss_dict': gloss_dict,
            'loss_weights': {
                'SeqCTC': 1.0,
                'ConvCTC': 1.0,
                'Dist': 25.0,
                'Diff': 10.0,
                'Gloss': 1.0
            },
            'weight_norm': True,
            'share_classifier': True,
            'use_diffusion': True,
            'diffusion_params': {
                'depth': 4,
                'num_heads': 8,
                'gloss_embedder_type': 'simple'
            }
        }
        
        # 创建模型
        model = SLRModel(**model_args)
        model.train()
        
        # 创建优化器
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        
        # 创建虚拟数据
        dataset = create_dummy_dataset(num_samples=4)
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        print("Model created successfully!")
        print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        # 测试几个训练步骤
        total_loss = 0
        num_steps = 3
        
        for step, (vid, vid_lgt, label, label_lgt) in enumerate(dataloader):
            if step >= num_steps:
                break
                
            print(f"\nStep {step + 1}/{num_steps}")
            print(f"Video shape: {vid.shape}")
            print(f"Video lengths: {vid_lgt}")
            print(f"Label shape: {label.shape}")
            print(f"Label lengths: {label_lgt}")
            
            # 前向传播
            optimizer.zero_grad()
            
            try:
                ret_dict = model(vid, vid_lgt, label=label, label_lgt=label_lgt)
                print(f"Forward pass successful!")
                print(f"Output keys: {list(ret_dict.keys())}")
                


                # 计算损失
                loss = model.criterion_calculation(ret_dict, label, label_lgt)
                print(f"Loss: {loss.item():.6f}")
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                print(f"Backward pass successful!")
                
            except Exception as e:
                print(f"Error in step {step + 1}: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        avg_loss = total_loss / num_steps
        print(f"\n✓ Training test passed!")
        print(f"Average loss: {avg_loss:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inference():
    """测试推理模式"""
    print("\nTesting inference mode...")
    
    try:
        from slr_network import SLRModel
        
        # 创建模型参数（简化版本用于推理）
        gloss_dict = {f'gloss_{i}': [i, f'gloss_{i}'] for i in range(1295)}
        
        model_args = {
            'num_classes': 1296,
            'c2d_type': 'resnet18',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 256,  # 减小隐藏层大小以加快推理
            'gloss_dict': gloss_dict,
            'weight_norm': True,
            'share_classifier': True,
            'use_diffusion': False,  # 推理时可以关闭diffusion
        }
        
        # 创建模型
        model = SLRModel(**model_args)
        model.eval()
        
        # 创建测试数据
        batch_size = 1
        seq_len = 32
        vid = torch.randn(batch_size, seq_len, 3, 224, 224)
        vid_lgt = torch.tensor([seq_len])
        
        print(f"Inference input shape: {vid.shape}")
        
        # 推理
        with torch.no_grad():
            ret_dict = model(vid, vid_lgt)
            
        print(f"Inference output keys: {list(ret_dict.keys())}")
        print(f"Sequence logits shape: {ret_dict['sequence_logits'].shape}")
        print(f"Recognized sentences: {ret_dict['recognized_sents']}")
        
        print("✓ Inference test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Inference test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_diffusion_components():
    """测试diffusion组件的独立功能"""
    print("\nTesting diffusion components...")
    
    try:
        from diffusion import GaussianDiffusion, generate_cosine_schedule
        from modules.DiT import DiT
        
        # 创建diffusion模型
        dit_model = DiT(hidden_size=512, depth=4, num_heads=8, num_classes=1296)
        
        # 创建diffusion调度器
        betas = generate_cosine_schedule(1000)
        diffusion = GaussianDiffusion(
            model=dit_model,
            betas=betas,
            loss_type="l2"
        )
        
        # 测试数据
        batch_size = 2
        seq_len = 50
        hidden_size = 512
        
        x_start = torch.randn(batch_size, seq_len, hidden_size)
        t = torch.randint(0, 1000, (batch_size,))
        
        # 测试前向扩散过程
        noise = torch.randn_like(x_start)
        x_noisy = diffusion.q_sample(x_start, t, noise)
        print(f"Noisy sample shape: {x_noisy.shape}")
        
        # 测试损失计算
        loss = diffusion.training_losses(dit_model, x_start, t)
        print(f"Diffusion loss: {loss['loss'].item():.6f}")
        
        print("✓ Diffusion components test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Diffusion components test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("DTA-2014T Integration Validation")
    print("=" * 60)
    
    tests = [
        test_diffusion_components,
        test_inference,
        test_training_step,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 40)
    
    print("\n" + "=" * 60)
    print(f"Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed!")
        print("✅ The integration is ready for training on real data!")
        print("\nNext steps:")
        print("1. Prepare your Phoenix2014-T dataset")
        print("2. Adjust hyperparameters in configs/baseline.yaml")
        print("3. Run: python main.py --config configs/baseline.yaml")
    else:
        print("⚠️  Some validation tests failed.")
        print("Please check the errors above before proceeding.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
