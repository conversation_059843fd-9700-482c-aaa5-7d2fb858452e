import pdb
import copy
import utils
import torch
import types
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from modules.criterions import SeqKD
from modules import BiLSTMLayer, TemporalConv
import modules.resnet as resnet
from modules.crossAttention import CrossAttention
from modules.DiT import DiT, MLP, PositionalEncoding
from modules.GlossEmbedder import create_gloss_embedder
from utils.Glossloss import Glossloss
from utils.Contrastive_Loss import Ctloss
from utils.Mseloss import MSELoss

class Identity(nn.Module):
    def __init__(self):
        super(Identity, self).__init__()

    def forward(self, x):
        return x


class NormLinear(nn.Module):
    def __init__(self, in_dim, out_dim):
        super(NormLinear, self).__init__()
        self.weight = nn.Parameter(torch.Tensor(in_dim, out_dim))
        nn.init.xavier_uniform_(self.weight, gain=nn.init.calculate_gain('relu'))

    def forward(self, x):
        outputs = torch.matmul(x, F.normalize(self.weight, dim=0))
        return outputs


class SLRModel(nn.Module):
    def __init__(
            self, num_classes, c2d_type, conv_type, use_bn=False,
            hidden_size=1024, gloss_dict=None, loss_weights=None,
            weight_norm=True, share_classifier=True, attention_params=None,
            use_diffusion=False, diffusion_params=None
    ):
        super(SLRModel, self).__init__()
        self.decoder = None
        self.loss = dict()
        self.num_classes = num_classes
        self.loss_weights = loss_weights
        self.use_diffusion = use_diffusion
        self.criterion_init()

        # 传递注意力参数给ResNet模型
        try:
            if attention_params is not None:
                self.conv2d = getattr(resnet, c2d_type)(attention_params=attention_params)
            else:
                self.conv2d = getattr(resnet, c2d_type)()
        except Exception as e:
            print(f"Error initializing ResNet with attention params: {e}")
            print(f"Falling back to default initialization")
            self.conv2d = getattr(resnet, c2d_type)()

        self.conv2d.fc = Identity()

        self.conv1d = TemporalConv(input_size=512,
                                   hidden_size=hidden_size,
                                   conv_type=conv_type,
                                   use_bn=use_bn,
                                   num_classes=num_classes)
        self.decoder = utils.Decode(gloss_dict, num_classes, 'beam')
        self.temporal_model = BiLSTMLayer(rnn_type='LSTM', input_size=hidden_size, hidden_size=hidden_size,
                                          num_layers=2, bidirectional=True)
        if weight_norm:
            self.classifier = NormLinear(hidden_size, self.num_classes)
            self.conv1d.fc = NormLinear(hidden_size, self.num_classes)
        else:
            self.classifier = nn.Linear(hidden_size, self.num_classes)
            self.conv1d.fc = nn.Linear(hidden_size, self.num_classes)
        if share_classifier:
            self.conv1d.fc = self.classifier

        # 添加Diffusion相关组件
        if self.use_diffusion:
            # 交叉注意力机制
            self.cross_attention = CrossAttention(dim=hidden_size)

            # Diffusion Transformer
            diffusion_config = diffusion_params or {}
            self.diffusion_model = DiT(
                hidden_size=hidden_size,
                depth=diffusion_config.get('depth', 8),
                num_heads=diffusion_config.get('num_heads', 8),
                num_classes=num_classes
            )

            # 位置编码
            self.pos_embed = PositionalEncoding(d_model=hidden_size)

            # Gloss嵌入器
            embedder_type = diffusion_config.get('gloss_embedder_type', 'simple')
            self.gloss_embedder = create_gloss_embedder(
                embedder_type=embedder_type,
                vocab_size=num_classes,
                hidden_size=hidden_size
            )

            # 特征变换MLP
            self.v2t = MLP(hidden_size)
            self.t2v = MLP(hidden_size)

            # 可学习的权重参数
            self.weights = nn.Parameter(torch.ones(2) / 2, requires_grad=True)

        #self.register_backward_hook(self.backward_hook)

    def backward_hook(self, module, grad_input, grad_output):
        for g in grad_input:
            g[g != g] = 0

    def masked_bn(self, inputs, len_x):
        def pad(tensor, length):
            return torch.cat([tensor, tensor.new(length - tensor.size(0), *tensor.size()[1:]).zero_()])

        x = torch.cat([inputs[len_x[0] * idx:len_x[0] * idx + lgt] for idx, lgt in enumerate(len_x)])
        x = self.conv2d(x)
        x = torch.cat([pad(x[sum(len_x[:idx]):sum(len_x[:idx + 1])], len_x[0])
                       for idx, lgt in enumerate(len_x)])
        return x

    def forward(self, x, len_x, label=None, label_lgt=None):
        if len(x.shape) == 5:
            # videos
            batch, temp, channel, height, width = x.shape
            #inputs = x.reshape(batch * temp, channel, height, width)
            #framewise = self.masked_bn(inputs, len_x)
            #framewise = framewise.reshape(batch, temp, -1).transpose(1, 2)
            framewise = self.conv2d(x.permute(0,2,1,3,4)).view(batch, temp, -1).permute(0,2,1) # btc -> bct
        else:
            # frame-wise features
            framewise = x

        conv1d_outputs = self.conv1d(framewise, len_x)
        # x: T, B, C
        x = conv1d_outputs['visual_feat']
        lgt = conv1d_outputs['feat_len']
        tm_outputs = self.temporal_model(x, lgt)

        # 如果使用diffusion模型，进行增强处理
        if self.use_diffusion and self.training:
            # 获取视觉特征
            visual_feat = tm_outputs['predictions'].permute(1, 0, 2)  # TBC -> BTC
            visual_feat = self.pos_embed(visual_feat)

            # 如果有标签，进行交叉注意力处理
            if label is not None and label_lgt is not None:
                # 使用Gloss嵌入器生成语义特征
                max_label_len = max(label_lgt)
                batch_size = visual_feat.size(0)

                # 创建gloss输入（填充到相同长度）
                gloss_input = torch.zeros(batch_size, max_label_len, dtype=torch.long, device=visual_feat.device)

                # 处理label：如果是1D张量，需要根据label_lgt分割
                if label.dim() == 1:
                    # 将扁平化的label按照label_lgt分割成batch
                    start_idx = 0
                    for i, label_len in enumerate(label_lgt):
                        if label_len > 0 and start_idx < label.size(0):
                            end_idx = min(start_idx + label_len, label.size(0))
                            actual_len = min(end_idx - start_idx, max_label_len)
                            if actual_len > 0:
                                gloss_input[i, :actual_len] = label[start_idx:start_idx + actual_len]
                            start_idx = end_idx
                else:
                    # 如果是2D张量，直接使用
                    for i, label_len in enumerate(label_lgt):
                        if label_len > 0 and i < label.size(0):
                            actual_len = min(label_len, label.size(1), max_label_len)
                            if actual_len > 0:
                                gloss_input[i, :actual_len] = label[i, :actual_len]

                # 生成gloss嵌入
                gloss_embed = self.gloss_embedder(gloss_input)

                # 交叉注意力
                enhanced_feat, attn = self.cross_attention(visual_feat, gloss_embed, gloss_embed, lgt, label_lgt)

                # 融合原始特征和增强特征
                visual_feat = self.weights[0] * visual_feat + self.weights[1] * enhanced_feat

                # 转换回TBC格式
                tm_outputs['predictions'] = visual_feat.permute(1, 0, 2)

        outputs = self.classifier(tm_outputs['predictions'])
        pred = None if self.training \
            else self.decoder.decode(outputs, lgt, batch_first=False, probs=False)
        conv_pred = None if self.training \
            else self.decoder.decode(conv1d_outputs['conv_logits'], lgt, batch_first=False, probs=False)

        ret_dict = {
            "feat_len": lgt,
            "conv_logits": conv1d_outputs['conv_logits'],
            "sequence_logits": outputs,
            "conv_sents": conv_pred,
            "recognized_sents": pred,
        }

        # 如果使用diffusion，添加额外的输出
        if self.use_diffusion and self.training:
            ret_dict.update({
                "visual_feat": tm_outputs['predictions'],
                "label_len": label_lgt,
            })

        return ret_dict

    def criterion_calculation(self, ret_dict, label, label_lgt, diffusion=None):
        loss = 0
        for k, weight in self.loss_weights.items():
            if k == 'ConvCTC':
                # 确保feat_len是正确的形状
                feat_len = ret_dict["feat_len"].cpu().int()
                if feat_len.dim() == 0:
                    feat_len = feat_len.unsqueeze(0)  # 标量转 (1,)

                loss_temp = self.loss['CTCLoss'](ret_dict["conv_logits"].log_softmax(-1),
                                                 label.cpu().int(), feat_len,
                                                 label_lgt.cpu().int()).mean()
                if not (np.isinf(loss_temp.item()) or np.isnan(loss_temp.item())):
                    loss += weight * loss_temp
            elif k == 'SeqCTC':
                # 确保feat_len是正确的形状
                feat_len = ret_dict["feat_len"].cpu().int()
                if feat_len.dim() == 0:
                    feat_len = feat_len.unsqueeze(0)  # 标量转 (1,)

                loss_temp = self.loss['CTCLoss'](ret_dict["sequence_logits"].log_softmax(-1),
                                                 label.cpu().int(), feat_len,
                                                 label_lgt.cpu().int()).mean()
                if not (np.isinf(loss_temp.item()) or np.isnan(loss_temp.item())):
                    loss += weight * loss_temp
            elif k == 'Dist':
                # 确保两个logits有相同的时间维度
                conv_logits = ret_dict["conv_logits"]
                seq_logits = ret_dict["sequence_logits"].detach()

                # 取较小的时间维度
                min_time = min(conv_logits.size(0), seq_logits.size(0))
                conv_logits_trimmed = conv_logits[:min_time]
                seq_logits_trimmed = seq_logits[:min_time]

                loss_temp = self.loss['distillation'](conv_logits_trimmed,
                                                      seq_logits_trimmed,
                                                      use_blank=False)
                if not (np.isinf(loss_temp.item()) or np.isnan(loss_temp.item())):
                    loss += weight * loss_temp
            elif k == 'Diff' and diffusion is not None and self.use_diffusion:
                # Diffusion损失
                if "visual_feat" in ret_dict and "label_len" in ret_dict:
                    loss_temp = diffusion(ret_dict["visual_feat"].transpose(0, 1).detach(),
                                         label, ret_dict["feat_len"].cpu().int(),
                                         label_len=ret_dict["label_len"].cpu().int())
                    if not (np.isinf(loss_temp.item()) or np.isnan(loss_temp.item())):
                        loss += weight * loss_temp
            elif k == 'Gloss' and self.use_diffusion:
                # Gloss损失（简化版本）
                if "visual_feat" in ret_dict:
                    try:
                        gloss_embed = torch.randn_like(ret_dict["visual_feat"])  # 简化的gloss嵌入
                        loss_temp = self.loss['GlossLoss'](ret_dict["visual_feat"], gloss_embed)
                        if not (np.isinf(loss_temp.item()) or np.isnan(loss_temp.item())):
                            loss += weight * loss_temp
                    except:
                        pass  # 如果计算失败，跳过这个损失
        return loss

    def criterion_init(self):
        self.loss['CTCLoss'] = torch.nn.CTCLoss(reduction='none', zero_infinity=False)
        self.loss['distillation'] = SeqKD(T=8)

        # 添加新的损失函数
        if self.use_diffusion:
            self.loss['GlossLoss'] = Glossloss()
            self.loss['ContrastiveLoss'] = Ctloss()
            self.loss['MSELoss'] = MSELoss()

        return self.loss
