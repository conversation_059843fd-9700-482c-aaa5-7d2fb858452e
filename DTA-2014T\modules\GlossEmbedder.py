import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Dict, Any

class SimpleGlossEmbedder(nn.Module):
    """
    简化版的Gloss嵌入器，不依赖MBart预训练模型
    可以在后续需要时替换为完整的MBart版本
    """
    def __init__(self, vocab_size: int, hidden_size: int = 512, max_length: int = 512):
        super(SimpleGlossEmbedder, self).__init__()
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.max_length = max_length
        
        # 词嵌入层
        self.embedding = nn.Embedding(vocab_size, hidden_size, padding_idx=0)
        
        # 位置编码
        self.pos_embedding = nn.Embedding(max_length, hidden_size)
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=8,
            dim_feedforward=hidden_size * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=6)
        
        # 输出投影层
        self.output_proj = nn.Linear(hidden_size, hidden_size)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        nn.init.normal_(self.embedding.weight, mean=0, std=0.02)
        nn.init.normal_(self.pos_embedding.weight, mean=0, std=0.02)
        
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.normal_(module.weight, mean=0, std=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input_ids: 输入的token ids, shape: (batch_size, seq_len)
            attention_mask: 注意力掩码, shape: (batch_size, seq_len)
        
        Returns:
            encoded_features: 编码后的特征, shape: (batch_size, seq_len, hidden_size)
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device
        
        # 词嵌入
        token_embeddings = self.embedding(input_ids)
        
        # 位置嵌入
        position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)
        position_embeddings = self.pos_embedding(position_ids)
        
        # 组合嵌入
        embeddings = token_embeddings + position_embeddings
        
        # 创建注意力掩码（如果没有提供）
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()
        
        # 转换掩码格式（Transformer需要的是key_padding_mask）
        key_padding_mask = (attention_mask == 0)
        
        # Transformer编码
        encoded = self.transformer(embeddings, src_key_padding_mask=key_padding_mask)
        
        # 输出投影
        output = self.output_proj(encoded)
        
        return output
    
    def encode_gloss_sequence(self, gloss_ids: torch.Tensor, lengths: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        编码gloss序列
        
        Args:
            gloss_ids: gloss的token ids
            lengths: 每个序列的实际长度
        
        Returns:
            encoded_glosses: 编码后的gloss特征
        """
        # 创建注意力掩码
        if lengths is not None:
            batch_size, max_len = gloss_ids.shape
            attention_mask = torch.zeros(batch_size, max_len, device=gloss_ids.device)
            for i, length in enumerate(lengths):
                attention_mask[i, :length] = 1
        else:
            attention_mask = None
        
        # 编码
        encoded = self.forward(gloss_ids, attention_mask)
        
        return encoded


class MBartGlossEmbedder(nn.Module):
    """
    基于MBart的Gloss嵌入器（需要transformers库）
    这是一个占位符实现，实际使用时需要安装transformers库
    """
    def __init__(self, model_name: str = "facebook/mbart-large-cc25", hidden_size: int = 512):
        super(MBartGlossEmbedder, self).__init__()
        self.hidden_size = hidden_size
        
        try:
            from transformers import MBartModel, MBartTokenizer
            self.tokenizer = MBartTokenizer.from_pretrained(model_name)
            self.model = MBartModel.from_pretrained(model_name)
            
            # 投影层，将MBart的输出维度映射到目标维度
            mbart_hidden_size = self.model.config.hidden_size
            if mbart_hidden_size != hidden_size:
                self.projection = nn.Linear(mbart_hidden_size, hidden_size)
            else:
                self.projection = nn.Identity()
                
            # 冻结MBart参数（可选）
            for param in self.model.parameters():
                param.requires_grad = False
                
        except ImportError:
            print("Warning: transformers library not found. Using SimpleGlossEmbedder instead.")
            # 回退到简单版本
            self.model = None
            self.tokenizer = None
            self.projection = nn.Linear(512, hidden_size)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        if self.model is None:
            # 如果MBart不可用，返回随机特征
            batch_size, seq_len = input_ids.shape
            return torch.randn(batch_size, seq_len, self.hidden_size, device=input_ids.device)
        
        # 使用MBart编码
        outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
        last_hidden_state = outputs.last_hidden_state
        
        # 投影到目标维度
        projected = self.projection(last_hidden_state)
        
        return projected
    
    def encode_text(self, texts: list, max_length: int = 512) -> Dict[str, torch.Tensor]:
        """
        编码文本列表
        
        Args:
            texts: 文本列表
            max_length: 最大长度
        
        Returns:
            包含input_ids和attention_mask的字典
        """
        if self.tokenizer is None:
            raise RuntimeError("Tokenizer not available")
        
        encoded = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=max_length,
            return_tensors="pt"
        )
        
        return encoded


def create_gloss_embedder(embedder_type: str = "simple", **kwargs) -> nn.Module:
    """
    创建Gloss嵌入器的工厂函数
    
    Args:
        embedder_type: 嵌入器类型 ("simple" 或 "mbart")
        **kwargs: 其他参数
    
    Returns:
        Gloss嵌入器实例
    """
    if embedder_type == "simple":
        return SimpleGlossEmbedder(**kwargs)
    elif embedder_type == "mbart":
        return MBartGlossEmbedder(**kwargs)
    else:
        raise ValueError(f"Unknown embedder type: {embedder_type}")
