#!/usr/bin/env python3
"""
测试脚本：验证Diffusion模块集成是否成功
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dit_module():
    """测试DiT模块"""
    print("Testing DiT module...")
    try:
        from modules.DiT import DiT, TimestepEmbedder, PositionalEncoding
        
        # 创建DiT模型
        model = DiT(hidden_size=512, depth=4, num_heads=8, num_classes=1296)
        
        # 创建测试输入
        batch_size = 2
        seq_len = 100
        hidden_size = 512
        
        g = torch.randn(batch_size, seq_len, hidden_size)
        t = torch.randint(0, 1000, (batch_size,))
        
        # 前向传播
        output = model(g, t)
        print(f"DiT output shape: {output.shape}")
        print("✓ DiT module test passed!")
        return True
        
    except Exception as e:
        print(f"✗ DiT module test failed: {e}")
        return False

def test_cross_attention():
    """测试交叉注意力模块"""
    print("\nTesting CrossAttention module...")
    try:
        from modules.crossAttention import CrossAttention
        
        # 创建交叉注意力模型
        model = CrossAttention(dim=512, heads=8)
        
        # 创建测试输入
        batch_size = 2
        seq_len1 = 100
        seq_len2 = 80
        hidden_size = 512
        
        queries = torch.randn(batch_size, seq_len1, hidden_size)
        keys = torch.randn(batch_size, seq_len2, hidden_size)
        values = torch.randn(batch_size, seq_len2, hidden_size)
        len_x = torch.tensor([seq_len2, seq_len2-10])
        lgt = torch.tensor([seq_len1, seq_len1-20])
        
        # 前向传播
        output, attn = model(queries, keys, values, len_x, lgt)
        print(f"CrossAttention output shape: {output.shape}")
        print(f"Attention shape: {attn.shape}")
        print("✓ CrossAttention module test passed!")
        return True
        
    except Exception as e:
        print(f"✗ CrossAttention module test failed: {e}")
        return False

def test_gloss_embedder():
    """测试Gloss嵌入器"""
    print("\nTesting GlossEmbedder...")
    try:
        from modules.GlossEmbedder import create_gloss_embedder

        # 测试简单版本的Gloss嵌入器
        embedder = create_gloss_embedder(
            embedder_type="simple",
            vocab_size=1296,
            hidden_size=512
        )

        # 创建测试输入
        batch_size = 2
        seq_len = 20
        input_ids = torch.randint(1, 1296, (batch_size, seq_len))

        # 前向传播
        output = embedder(input_ids)
        print(f"GlossEmbedder output shape: {output.shape}")

        # 测试编码gloss序列
        lengths = torch.tensor([seq_len, seq_len-5])
        encoded = embedder.encode_gloss_sequence(input_ids, lengths)
        print(f"Encoded gloss sequence shape: {encoded.shape}")

        print("✓ GlossEmbedder test passed!")
        return True

    except Exception as e:
        print(f"✗ GlossEmbedder test failed: {e}")
        return False

def test_loss_functions():
    """测试损失函数"""
    print("\nTesting loss functions...")
    try:
        from utils.Glossloss import Glossloss
        from utils.Contrastive_Loss import Ctloss
        from utils.Mseloss import MSELoss

        # 测试Gloss损失
        gloss_loss = Glossloss()
        x = torch.randn(2, 100, 512)
        g = torch.randn(2, 80, 512)
        loss1 = gloss_loss(x, g)
        print(f"Gloss loss: {loss1.item()}")

        # 测试MSE损失
        mse_loss = MSELoss()
        pred = torch.randn(2, 100, 512)
        target = torch.randn(2, 100, 512)
        loss2 = mse_loss(pred, target)
        print(f"MSE loss: {loss2.item()}")

        print("✓ Loss functions test passed!")
        return True

    except Exception as e:
        print(f"✗ Loss functions test failed: {e}")
        return False

def test_slr_model():
    """测试集成后的SLR模型"""
    print("\nTesting integrated SLR model...")
    try:
        from slr_network import SLRModel
        
        # 创建模型参数
        # 创建一个简单的gloss字典用于测试
        gloss_dict = {f'gloss_{i}': [i, f'gloss_{i}'] for i in range(1295)}

        model_args = {
            'num_classes': 1296,
            'c2d_type': 'resnet18',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 512,
            'gloss_dict': gloss_dict,
            'loss_weights': {
                'SeqCTC': 1.0,
                'ConvCTC': 1.0,
                'Dist': 25.0,
                'Diff': 10.0,
                'Gloss': 1.0
            },
            'weight_norm': True,
            'share_classifier': True,
            'use_diffusion': True,
            'diffusion_params': {
                'depth': 4,
                'num_heads': 8
            }
        }
        
        # 创建模型
        model = SLRModel(**model_args)
        model.eval()
        
        # 创建测试输入
        batch_size = 2
        seq_len = 64
        channels = 3
        height = 224
        width = 224
        
        # 视频输入 (batch, time, channels, height, width)
        vid = torch.randn(batch_size, seq_len, channels, height, width)
        vid_lgt = torch.tensor([seq_len, seq_len-10])
        label = torch.randint(0, 1296, (batch_size, 20))
        label_lgt = torch.tensor([20, 18])
        
        # 前向传播
        with torch.no_grad():
            ret_dict = model(vid, vid_lgt, label=label, label_lgt=label_lgt)
        
        print(f"Model output keys: {ret_dict.keys()}")
        print(f"Sequence logits shape: {ret_dict['sequence_logits'].shape}")
        print(f"Conv logits shape: {ret_dict['conv_logits'].shape}")
        print("✓ Integrated SLR model test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Integrated SLR model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("DTA-2014T Diffusion Integration Test")
    print("=" * 60)
    
    tests = [
        test_dit_module,
        test_cross_attention,
        test_gloss_embedder,
        test_loss_functions,
        test_slr_model
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Integration successful!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
