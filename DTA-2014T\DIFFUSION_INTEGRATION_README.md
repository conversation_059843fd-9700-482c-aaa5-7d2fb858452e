# DTA-2014T Diffusion Integration

## 概述

本项目成功将Continuous-Sign-Language-Recognition-Based-on-Diffusion项目中的创新技术集成到DTA-2014T项目中，旨在降低连续手语识别的词错误率(WER)。

## 集成的主要组件

### 1. Diffusion Transformer (DiT) 模块
- **文件**: `modules/DiT.py`
- **功能**: 基于扩散模型的Transformer架构，提供更好的序列建模能力
- **关键类**:
  - `DiT`: 主要的扩散Transformer模型
  - `TimestepEmbedder`: 时间步嵌入器
  - `PositionalEncoding`: 位置编码
  - `DiTBlock`: DiT块，包含自适应层归一化

### 2. 交叉注意力机制
- **文件**: `modules/crossAttention.py`
- **功能**: 实现视觉特征和文本特征之间的交叉注意力
- **关键类**:
  - `CrossAttention`: 基础交叉注意力模块
  - `CrossAttention_Perciever`: 带有Transformer编码器的增强版本

### 3. 扩散模型
- **文件**: `diffusion.py`
- **功能**: 高斯扩散模型实现，支持噪声调度和采样
- **关键类**:
  - `GaussianDiffusion`: 主要的扩散模型类
  - `EMA`: 指数移动平均，用于模型参数更新

### 4. 新增损失函数
- **Gloss损失** (`utils/Glossloss.py`): 用于语义级别的监督
- **对比损失** (`utils/Contrastive_Loss.py`): 增强特征对齐
- **MSE损失** (`utils/Mseloss.py`): 均方误差损失

## 配置更新

### 模型参数 (configs/baseline.yaml)
```yaml
model_args:
  # 启用diffusion功能
  use_diffusion: True
  diffusion_params:
    depth: 8          # DiT深度
    num_heads: 8      # 注意力头数

# 新增损失权重
loss_weights:
  SeqCTC: 1.0
  ConvCTC: 1.0
  Dist: 25.0
  Diff: 10.0        # Diffusion损失权重
  Gloss: 1.0        # Gloss损失权重
```

## 主要改进

### 1. 网络架构增强 (slr_network.py)
- 集成了Diffusion Transformer模块
- 添加了交叉注意力机制
- 支持多种新的损失函数
- 可学习的特征融合权重

### 2. 训练流程优化 (seq_scripts.py)
- 支持diffusion模型的训练
- 集成了高斯扩散调度器
- 改进的损失计算流程

## 使用方法

### 1. 基础训练
```bash
# 使用原始配置（不启用diffusion）
python main.py --config configs/baseline.yaml

# 启用diffusion功能
# 修改baseline.yaml中的use_diffusion: True
python main.py --config configs/baseline.yaml
```

### 2. 测试集成
```bash
# 运行集成测试
python test_integration.py
```

### 3. 自定义配置
可以通过修改`configs/baseline.yaml`中的参数来调整模型行为：

- `use_diffusion`: 是否启用diffusion功能
- `diffusion_params.depth`: DiT模型深度
- `diffusion_params.num_heads`: 注意力头数
- `loss_weights`: 各种损失的权重

## 技术特点

### 1. 模块化设计
- 所有新组件都是可选的，可以独立启用/禁用
- 保持与原始DTA-2014T架构的兼容性

### 2. 设备兼容性
- 自动处理CPU/GPU设备分配
- 支持多GPU训练

### 3. 损失函数组合
- 支持多种损失函数的加权组合
- 自动处理NaN和Inf值

## 预期改进

通过集成这些创新技术，预期能够实现：

1. **降低WER**: 通过更好的序列建模和特征对齐
2. **提高鲁棒性**: 通过diffusion模型的去噪能力
3. **增强语义理解**: 通过gloss级别的监督学习
4. **改善特征表示**: 通过交叉注意力机制

## 文件结构

```
DTA-2014T/
├── modules/
│   ├── DiT.py                 # Diffusion Transformer
│   ├── crossAttention.py     # 交叉注意力
│   └── ...
├── utils/
│   ├── Glossloss.py          # Gloss损失
│   ├── Contrastive_Loss.py   # 对比损失
│   ├── Mseloss.py            # MSE损失
│   └── ...
├── diffusion.py              # 扩散模型
├── slr_network.py            # 主网络（已修改）
├── seq_scripts.py            # 训练脚本（已修改）
├── test_integration.py       # 集成测试
└── configs/baseline.yaml     # 配置文件（已更新）
```

## 注意事项

1. 确保安装了所需的依赖包（特别是timm库）
2. 首次使用时建议先运行测试脚本验证集成
3. 根据GPU内存调整batch_size和模型参数
4. 可以根据数据集特点调整损失权重

## 故障排除

如果遇到问题，请：
1. 运行`python test_integration.py`检查集成状态
2. 检查CUDA版本和PyTorch兼容性
3. 确认所有依赖包已正确安装
4. 查看训练日志中的错误信息
