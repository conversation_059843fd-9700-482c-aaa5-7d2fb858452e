import torch
import torch.nn as nn
import numpy as np


class Glossloss(nn.Module):

    def __init__(self):
        super(Glossloss, self).__init__()
        self.celoss = nn.CrossEntropyLoss(reduction='mean')

    def forward(self, x, g):
        matrix = torch.matmul(x, g.transpose(1, 2)).to(x.device)
        index = torch.max(matrix, 1)[1].to(x.device)
        label = torch.zeros(matrix.size()).to(x.device)
        for i in range(matrix.size(0)):
            label[i][index] = 1
        loss = self.celoss(matrix, label)
        return loss
