import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class CrossAttention(nn.Module):
    def __init__(self, dim, heads=8):
        super().__init__()
        self.dim = dim
        self.heads = heads
        self.scale = dim ** -0.5
        self.query = nn.Linear(dim, dim)
        self.key = nn.Linear(dim, dim)
        self.value = nn.Linear(dim, dim)

    def forward(self, queries, keys, values, len_x, lgt):
        b, n, _, h = *queries.shape, self.heads
        b2, n2, _ = keys.shape

        # 分到两张GPU上训练，相当于batch_size=1, 所以不用mask
        queries = self.query(queries)
        keys = self.key(keys)
        values = self.value(values)

        queries = queries.view(b, n, h, -1).transpose(1, 2)
        keys = keys.view(b2, n2, h, -1).transpose(1, 2)
        values = values.view(b2, n2, h, -1).transpose(1, 2)

        dots = torch.einsum('bhid, bhjd->bhij', queries, keys) * self.scale
        if b == 2:
            maskK = get_mask2(n2, len_x)
            mask = maskK.unsqueeze(1).unsqueeze(1).to(dots.device)
            mask = mask == 0
            dots = dots.masked_fill(mask, float("-inf"))
        
        attn = dots.softmax(dim=-1)

        out = torch.einsum('bhij, bhjd->bhid', attn, values)
        out = out.transpose(1, 2).contiguous().view(b, n, -1)
        attn2 = attn.mean(dim=1)
        return out, attn2


class CrossAttention_Perciever(nn.Module):
    def __init__(self, dim, num_layer):
        super().__init__()
        self.ca = CrossAttention(dim)
        transformerEncodeLayer = nn.TransformerEncoderLayer(d_model=dim, nhead=8, batch_first=True)
        self.transformerEncoder = nn.TransformerEncoder(transformerEncodeLayer, num_layers=num_layer)

    def forward(self, queries, keys, values, len_x, lgt):
        b, n, _ = queries.shape
        crossAtten, attn = self.ca(queries, keys, values, len_x, lgt)

        maxlen = lgt[0] if lgt[0] > lgt[1] else lgt[1]
        maxlen = int(maxlen)
        mask = get_mask(maxlen, lgt)
        mask = mask.to(crossAtten.device)

        out = self.transformerEncoder(crossAtten, src_key_padding_mask=mask)
        return out, attn


class TransEncoder(nn.Module):
    def __init__(self, dim, layers):
        super().__init__()
        transformerEncodeLayer = nn.TransformerEncoderLayer(d_model=dim, nhead=8)
        self.transformerEncoder = nn.TransformerEncoder(transformerEncodeLayer, num_layers=layers)
    
    def forward(self, x, lgt):
        n, b, _ = x.shape

        mask = get_mask(n, lgt)
        mask = mask.to(x.device)

        out = self.transformerEncoder(x, src_key_padding_mask=mask)
        return out


def get_mask(seq_len, len):
    # 确保len是张量
    if not isinstance(len, torch.Tensor):
        len = torch.tensor(len)

    # 如果len是标量，转换为1维张量
    if len.dim() == 0:
        len = len.unsqueeze(0)

    n = len.shape[0]
    mask = torch.empty((1, seq_len))
    for i in range(n):
        mask_temp = torch.ones(seq_len) == 1
        for k in range(int(len[i])):
            mask_temp[k] = False
        mask_temp = mask_temp.unsqueeze(0)
        mask = torch.concat([mask, mask_temp], dim=0)
    mask = mask[1:, :]
    return mask

def get_mask2(seq_len, len):
    # 确保len是张量
    if not isinstance(len, torch.Tensor):
        len = torch.tensor(len)

    # 如果len是标量，转换为1维张量
    if len.dim() == 0:
        len = len.unsqueeze(0)

    n = len.shape[0]
    mask = torch.empty((1, seq_len))
    for i in range(n):
        mask_temp = torch.zeros(seq_len)
        # 确保索引不超出边界
        valid_len = min(int(len[i]), seq_len)
        for k in range(valid_len):
            mask_temp[k] = 1
        mask_temp = mask_temp.unsqueeze(0)
        mask = torch.concat([mask, mask_temp], dim=0)
    mask = mask[1:, :]
    return mask

def get_mask3(seq_len, len):
    # 确保len是张量
    if not isinstance(len, torch.Tensor):
        len = torch.tensor(len)

    # 如果len是标量，转换为1维张量
    if len.dim() == 0:
        len = len.unsqueeze(0)

    n = len.shape[0]
    mask = torch.empty((1, seq_len))
    for i in range(n):
        mask_temp = torch.zeros(seq_len)
        for k in range(int(len[i])):
            mask_temp[k] = 1
        mask_temp = mask_temp.unsqueeze(0)
        mask = torch.concat([mask, mask_temp], dim=0)
    mask = mask[1:, :]
    return mask
